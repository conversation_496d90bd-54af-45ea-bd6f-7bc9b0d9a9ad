spec:
  inputs:
    VERSION:
      type: string
      regex: '^QCE_V.*'  # 确保 VERSION 以 QCE_V 开头
      description: "请输入版本号，例如 QCE_V7 或 QCE_V7_SP1"
    PATCH:
      type: string
      default: ""  # 默认值为空，使其可选
      description: "请输入补丁号，例如 2603（可选）"

stages:
  - preparation
  - quality-assurance
  - build
  - deployment

install_npm_packages:
  stage: preparation
  image: node:22.16
  tags:
    - docker
  cache:
    paths:
      - node_modules
  script:
    - yarn --registry=http://r.npm.internal.yunify.com install

ensure_code_style_consistency:
  stage: quality-assurance
  image: node:22.16
  tags:
    - docker
  cache:
    paths:
      - node_modules
  script:
    - yarn lint

build_for_dev_envs:
  stage: build
  image: node:22.16
  rules:
    - if: $CI_COMMIT_BRANCH =~ /^(testing|staging)$/
  tags:
    - docker
  cache:
    paths:
      - node_modules
  artifacts:
    expire_in: 1h
    paths:
      - pitrix-webconsole-test.tgz
  script:
    - yarn run build
    - mv dist pitrix-webconsole-test
    - tar -czf pitrix-webconsole-test.tgz pitrix-webconsole-test

build_for_production:
  stage: build
  image: node:22.16
  rules:
    - if: $CI_COMMIT_BRANCH == "main"
  tags:
    - docker
  cache:
    paths:
      - node_modules
  artifacts:
    expire_in: 1h
    paths:
      - pitrix-webconsole-test.tgz
  script:
    - yarn run build
    - mv dist pitrix-webconsole-test
    - tar -czf pitrix-webconsole-test.tgz pitrix-webconsole-test

upload_qce_tar:
  stage: deployment
  when: manual
  rules:
    - if: '$CI_COMMIT_BRANCH =~ /^QCE_V[0-9.]+(-[0-9]+)?$/'
      when: manual
  tags:
    - shell
  dependencies:
    - build_for_production
  script:
    - |
      if [[ "$CI_COMMIT_BRANCH" =~ ^(QCE_V[0-9.]+)(-([0-9]+))?$ ]]; then
        MAJOR_VER="${BASH_REMATCH[1]}"
        PATCH_VER="${BASH_REMATCH[3]}"
        echo "Project name: $CI_PROJECT_NAME, Major version: $MAJOR_VER, Patch version: $PATCH_VER"
        MAJOR_NO_PREFIX=${MAJOR_VER#QCE_}
        BASH_PATH=/pitrix/repo/web/web.$VERSION_NO_PREFIX/
        LATEST_PATH=$BASH_PATH/latest
        if [ -z "$PATCH_VER" ]; then TARGET_PATH="$BASH_PATH/$PATCH_VER"; else TARGET_PATH="$BASH_PATH/MAJOR_VER"; fi
        ssh $QCE_REPO "mkdir -p $LATEST_PATH $TARGET_PATH"
        rsync -avz pitrix-webconsole-test.tgz $QCE_REPO:$LATEST_PATH/pitrix-webconsole-test.tar.gz
        ssh $QCE_REPO "rsync -avz $LATEST_PATH/pitrix-webconsole-test.tar.gz $TARGET_PATH/pitrix-webconsole-test.tar.gz"
      else
        echo "不支持的分支命名格式: $CI_COMMIT_BRANCH"
        exit 1
      fi
